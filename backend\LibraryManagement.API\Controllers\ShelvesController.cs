using LibraryManagement.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Entities;
using System.ComponentModel.DataAnnotations;

namespace LibraryManagement.API.Controllers
{
    [ApiController]
    [Route("api/shelves")]
    [AllowAnonymous] // Allow anonymous access for all endpoints
    public class ShelvesController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public ShelvesController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        // Standard API response wrapper
        private IActionResult ApiResponse<T>(T data, string message = "Success", bool success = true)
        {
            return Ok(new
            {
                success = success,
                message = message,
                data = data
            });
        }

        private IActionResult ApiError(string message, int statusCode = 400, string[] errors = null)
        {
            return StatusCode(statusCode, new
            {
                success = false,
                message = message,
                errors = errors ?? new string[0]
            });
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var shelves = await _unitOfWork.Bookshelves
                    .Query()
                    .Include(s => s.Zone)
                    .Include(s => s.Books)
                    .Select(s => new BookshelfDto
                    {
                        Id = s.Id,
                        Name = s.Name,
                        ZoneId = s.ZoneId,
                        ZoneName = s.Zone.Name,
                        Capacity = s.Capacity,
                        Status = s.Status,
                        Description = s.Description,
                        CurrentCount = s.Books.Sum(b => b.OnShelfQuantity), // Use OnShelfQuantity for shelf count
                        CreatedAt = s.CreatedAt,
                        UpdatedAt = s.UpdatedAt
                    })
                    .ToListAsync();

                // Add calculated fields
                var result = shelves.Select(s => new
                {
                    s.Id,
                    s.Name,
                    s.ZoneId,
                    s.ZoneName,
                    s.Capacity,
                    s.Status,
                    s.Description,
                    s.CurrentCount,
                    AvailableSpace = s.Capacity - s.CurrentCount,
                    s.CreatedAt,
                    s.UpdatedAt
                }).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi tải danh sách kệ sách", 500, new[] { ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var shelf = await _unitOfWork.Bookshelves
                    .Query()
                    .Include(s => s.Zone)
                    .Include(s => s.Books)
                    .Where(s => s.Id == id)
                    .Select(s => new
                    {
                        Id = s.Id,
                        Name = s.Name,
                        ZoneId = s.ZoneId,
                        ZoneName = s.Zone.Name,
                        Capacity = s.Capacity,
                        Status = s.Status,
                        Description = s.Description,
                        CurrentCount = s.Books.Sum(b => b.OnShelfQuantity),
                        AvailableSpace = s.Capacity - s.Books.Sum(b => b.OnShelfQuantity),
                        CreatedAt = s.CreatedAt,
                        UpdatedAt = s.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (shelf == null)
                    return ApiError("Không tìm thấy kệ sách", 404);

                return Ok(shelf);
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi tải thông tin kệ sách", 500, new[] { ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateShelfDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToArray();
                    return ApiError("Dữ liệu không hợp lệ", 400, errors);
                }

                // Check if shelf name already exists in the same zone
                var existingShelf = await _unitOfWork.Bookshelves
                    .Query()
                    .FirstOrDefaultAsync(s => s.Name == dto.Name && s.ZoneId == dto.ZoneId);

                if (existingShelf != null)
                    return ApiError("Tên kệ đã tồn tại trong khu vực này", 409);

                // Check if zone exists
                var zone = await _unitOfWork.Zones.GetByIdAsync(dto.ZoneId);
                if (zone == null)
                    return ApiError("Khu vực không tồn tại", 404);

                var shelf = new Bookshelf
                {
                    Name = dto.Name,
                    ZoneId = dto.ZoneId,
                    Description = dto.Description,
                    Capacity = dto.Capacity,
                    CurrentCount = 0,
                    Status = dto.Status ?? "Active",
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.Bookshelves.AddAsync(shelf);
                await _unitOfWork.SaveAsync();

                return ApiResponse(new { shelfId = shelf.Id }, "Tạo kệ sách thành công");
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi tạo kệ sách", 500, new[] { ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] CreateShelfDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToArray();
                    return ApiError("Dữ liệu không hợp lệ", 400, errors);
                }

                var shelf = await _unitOfWork.Bookshelves.GetByIdAsync(id);
                if (shelf == null)
                    return ApiError("Không tìm thấy kệ sách", 404);

                // Check if new name conflicts with existing shelf in same zone
                var existingShelf = await _unitOfWork.Bookshelves
                    .Query()
                    .FirstOrDefaultAsync(s => s.Name == dto.Name && s.ZoneId == dto.ZoneId && s.Id != id);

                if (existingShelf != null)
                    return ApiError("Tên kệ đã tồn tại trong khu vực này", 409);

                // Check if zone exists
                var zone = await _unitOfWork.Zones.GetByIdAsync(dto.ZoneId);
                if (zone == null)
                    return ApiError("Khu vực không tồn tại", 404);

                // Check if new capacity is not less than current book count
                var currentBookCount = await _unitOfWork.Books
                    .Query()
                    .Where(b => b.BookshelfId == id)
                    .SumAsync(b => b.StockQuantity);

                if (dto.Capacity < currentBookCount)
                    return ApiError($"Sức chứa mới ({dto.Capacity}) không thể nhỏ hơn số sách hiện tại ({currentBookCount})", 400);

                shelf.Name = dto.Name;
                shelf.ZoneId = dto.ZoneId;
                shelf.Description = dto.Description;
                shelf.Capacity = dto.Capacity;
                shelf.Status = dto.Status ?? shelf.Status;
                shelf.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.Bookshelves.UpdateAsync(shelf);
                await _unitOfWork.SaveAsync();

                return ApiResponse<object>(null, "Cập nhật kệ sách thành công");
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi cập nhật kệ sách", 500, new[] { ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var shelf = await _unitOfWork.Bookshelves
                    .Query()
                    .Include(s => s.Books)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (shelf == null)
                    return ApiError("Không tìm thấy kệ sách", 404);

                // Check if shelf has books
                if (shelf.Books.Any())
                    return ApiError("Không thể xóa kệ có chứa sách. Vui lòng di chuyển sách trước khi xóa.", 400);

                await _unitOfWork.Bookshelves.DeleteAsync(shelf);
                await _unitOfWork.SaveAsync();

                return ApiResponse<object>(null, "Xóa kệ sách thành công");
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi xóa kệ sách", 500, new[] { ex.Message });
            }
        }

        [HttpGet("{shelfId}/books")]
        public async Task<IActionResult> GetBooksInShelf(int shelfId)
        {
            try
            {
                var shelf = await _unitOfWork.Bookshelves.GetByIdAsync(shelfId);
                if (shelf == null)
                    return ApiError("Không tìm thấy kệ sách", 404);

                var books = await _unitOfWork.Books
                    .Query()
                    .Include(b => b.Category)
                    .Include(b => b.Bookshelf)
                    .ThenInclude(s => s.Zone)
                    .Where(b => b.BookshelfId == shelfId)
                    .Select(b => new
                    {
                        BookId = b.Id,
                        Title = b.Title,
                        Author = b.Author,
                        ISBN = b.ISBN,
                        CategoryName = b.Category.Name,
                        LocationCode = b.LocationCode,
                        StockQuantity = b.OnShelfQuantity, // Số lượng trên kệ này
                        OnShelfQuantity = b.OnShelfQuantity, // Số lượng trên kệ này
                        TotalQuantity = b.OnShelfQuantity, // Cho tương thích với frontend
                        ShelfLocation = $"{b.Bookshelf.Zone.Name} - {b.Bookshelf.Name}" + (b.LocationCode != null ? $" - {b.LocationCode}" : "")
                    })
                    .ToListAsync();

                return Ok(books);
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi tải danh sách sách trong kệ", 500, new[] { ex.Message });
            }
        }

        [HttpGet("{shelfId}/locations")]
        public async Task<IActionResult> GetAvailableLocations(int shelfId)
        {
            try
            {
                var shelf = await _unitOfWork.Bookshelves
                    .Query()
                    .Include(s => s.Books)
                    .FirstOrDefaultAsync(s => s.Id == shelfId);

                if (shelf == null)
                    return ApiError("Không tìm thấy kệ sách", 404);

                // Generate location codes (A1, A2, B1, B2, etc.)
                var allLocations = new List<string>();
                for (char row = 'A'; row <= 'Z' && allLocations.Count < shelf.Capacity; row++)
                {
                    for (int col = 1; col <= 10 && allLocations.Count < shelf.Capacity; col++)
                    {
                        allLocations.Add($"{row}{col}");
                    }
                }

                var usedLocations = shelf.Books
                    .Where(b => !string.IsNullOrEmpty(b.LocationCode))
                    .Select(b => b.LocationCode)
                    .Distinct()
                    .ToList();

                var availableLocations = allLocations
                    .Where(loc => !usedLocations.Contains(loc))
                    .ToList();

                var result = new
                {
                    AvailableLocations = availableLocations,
                    UsedLocations = usedLocations,
                    Capacity = shelf.Capacity,
                    CurrentCount = shelf.Books.Sum(b => b.StockQuantity)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi tải thông tin vị trí", 500, new[] { ex.Message });
            }
        }

        [HttpPost("assign-book")]
        public async Task<IActionResult> AssignBookToShelf([FromBody] AssignBookToShelfDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToArray();
                    return ApiError("Dữ liệu không hợp lệ", 400, errors);
                }

                // Check if shelf exists
                var shelf = await _unitOfWork.Bookshelves.GetByIdAsync(dto.ShelfId);
                if (shelf == null)
                    return ApiError("Không tìm thấy kệ sách", 404);

                // Check if book exists
                var book = await _unitOfWork.Books.GetByIdAsync(dto.BookId);
                if (book == null)
                    return ApiError("Không tìm thấy sách", 404);

                // Check if book has enough stock in warehouse
                if (book.StockQuantity < dto.Quantity)
                    return ApiError($"Không đủ sách trong kho. Có sẵn: {book.StockQuantity}, yêu cầu: {dto.Quantity}", 400);

                // Check if shelf has enough capacity
                var currentCount = await _unitOfWork.Books
                    .Query()
                    .Where(b => b.BookshelfId == dto.ShelfId)
                    .SumAsync(b => b.OnShelfQuantity);

                if (currentCount + dto.Quantity > shelf.Capacity)
                    return ApiError($"Kệ không đủ chỗ. Còn trống: {shelf.Capacity - currentCount}, yêu cầu: {dto.Quantity}", 400);

                // Check if book is already on this shelf
                if (book.BookshelfId == dto.ShelfId)
                {
                    // Book already exists on this shelf, just add quantity
                    book.StockQuantity -= dto.Quantity;
                    book.OnShelfQuantity += dto.Quantity;
                    book.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    // Move book from warehouse/another shelf to this shelf
                    book.StockQuantity -= dto.Quantity;
                    book.OnShelfQuantity += dto.Quantity;
                    book.BookshelfId = dto.ShelfId;
                    book.LocationCode = dto.LocationCode;
                    book.UpdatedAt = DateTime.UtcNow;
                }

                await _unitOfWork.Books.UpdateAsync(book);

                await _unitOfWork.SaveAsync();

                return ApiResponse<object>(null, "Thêm sách vào kệ thành công");
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi thêm sách vào kệ", 500, new[] { ex.Message });
            }
        }

        [HttpPost("remove-book")]
        public async Task<IActionResult> RemoveBookFromShelf([FromBody] RemoveBookFromShelfDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToArray();
                    return ApiError("Dữ liệu không hợp lệ", 400, errors);
                }

                // Find book on shelf
                var book = await _unitOfWork.Books.GetByIdAsync(dto.BookId);
                if (book == null)
                    return ApiError("Không tìm thấy sách", 404);

                if (book.BookshelfId != dto.ShelfId)
                    return ApiError("Sách không có trong kệ này", 400);

                // Determine quantity to remove
                var quantityToRemove = dto.Quantity ?? book.OnShelfQuantity;

                if (quantityToRemove > book.OnShelfQuantity)
                    return ApiError($"Không thể xóa {quantityToRemove} cuốn. Chỉ có {book.OnShelfQuantity} cuốn trên kệ", 400);

                // Move quantity back to warehouse
                book.StockQuantity += quantityToRemove;
                book.OnShelfQuantity -= quantityToRemove;

                // If no books left on shelf, remove from shelf
                if (book.OnShelfQuantity == 0)
                {
                    book.BookshelfId = null;
                    book.LocationCode = null;
                }

                book.UpdatedAt = DateTime.UtcNow;
                await _unitOfWork.Books.UpdateAsync(book);
                await _unitOfWork.SaveAsync();

                return ApiResponse<object>(null, "Xóa sách khỏi kệ thành công");
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi xóa sách khỏi kệ", 500, new[] { ex.Message });
            }
        }

        [HttpGet("search")]
        public async Task<IActionResult> SearchShelves([FromQuery] string q)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q))
                    return Ok(new List<object>());

                var shelves = await _unitOfWork.Bookshelves
                    .Query()
                    .Include(s => s.Zone)
                    .Include(s => s.Books)
                    .Where(s => s.Name.Contains(q) || s.Zone.Name.Contains(q))
                    .Select(s => new
                    {
                        Id = s.Id,
                        Name = s.Name,
                        ZoneId = s.ZoneId,
                        ZoneName = s.Zone.Name,
                        Capacity = s.Capacity,
                        Status = s.Status,
                        Description = s.Description,
                        CurrentCount = s.Books.Sum(b => b.StockQuantity),
                        AvailableSpace = s.Capacity - s.Books.Sum(b => b.StockQuantity)
                    })
                    .ToListAsync();

                return Ok(shelves);
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi tìm kiếm kệ sách", 500, new[] { ex.Message });
            }
        }

        [HttpPost("validate")]
        public async Task<IActionResult> ValidateShelf([FromBody] CreateShelfDto dto)
        {
            try
            {
                var errors = new List<string>();
                var warnings = new List<string>();

                // Basic validation
                if (string.IsNullOrWhiteSpace(dto.Name))
                    errors.Add("Tên kệ là bắt buộc");

                if (dto.ZoneId <= 0)
                    errors.Add("Vui lòng chọn khu vực");

                if (dto.Capacity <= 0)
                    errors.Add("Sức chứa phải lớn hơn 0");

                // Check zone exists
                var zone = await _unitOfWork.Zones.GetByIdAsync(dto.ZoneId);
                if (zone == null)
                    errors.Add("Khu vực không tồn tại");

                // Check duplicate name in zone
                var existingShelf = await _unitOfWork.Bookshelves
                    .Query()
                    .FirstOrDefaultAsync(s => s.Name == dto.Name && s.ZoneId == dto.ZoneId);

                if (existingShelf != null)
                    errors.Add("Tên kệ đã tồn tại trong khu vực này");

                // Warnings
                if (dto.Capacity > 500)
                    warnings.Add("Sức chứa lớn có thể khó quản lý");

                var result = new
                {
                    IsValid = errors.Count == 0,
                    Errors = errors,
                    Warnings = warnings
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return ApiError("Lỗi khi kiểm tra dữ liệu", 500, new[] { ex.Message });
            }
        }
    }
}
