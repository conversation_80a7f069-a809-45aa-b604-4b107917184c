using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Core.Entities;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Enums;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class BorrowsController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public BorrowsController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> GetBorrowRecords()
    {
        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();

        var bookDict = books.ToDictionary(b => b.Id, b => b);
        var memberDict = members.ToDictionary(m => m.Id, m => m);

        var borrowRecordDtos = borrowRecords.Select(br => new BorrowRecordDto
        {
            Id = br.Id,
            BookId = br.BookId,
            BookTitle = bookDict.GetValueOrDefault(br.BookId)?.Title ?? "Unknown",
            BookAuthor = bookDict.GetValueOrDefault(br.BookId)?.Author ?? "Unknown",
            MemberId = br.MemberId,
            MemberName = memberDict.GetValueOrDefault(br.MemberId)?.FullName ?? "Unknown",
            BorrowDate = br.BorrowDate,
            DueDate = br.DueDate,
            ReturnDate = br.ReturnDate,
            Status = br.Status,
            StatusName = GetStatusName(br.Status),
            Notes = br.Notes,
            Fine = br.Fine,
            IsOverdue = br.IsOverdue,
            DaysOverdue = br.DaysOverdue,
            CreatedAt = br.CreatedAt
        }).OrderByDescending(br => br.BorrowDate).ToList();

        return Ok(borrowRecordDtos);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<BorrowRecordDto>> GetBorrowRecord(int id)
    {
        var borrowRecord = await _unitOfWork.BorrowRecords.GetByIdAsync(id);
        if (borrowRecord == null)
        {
            return NotFound();
        }

        var book = await _unitOfWork.Books.GetByIdAsync(borrowRecord.BookId);
        var member = await _unitOfWork.Members.GetByIdAsync(borrowRecord.MemberId);

        var borrowRecordDto = new BorrowRecordDto
        {
            Id = borrowRecord.Id,
            BookId = borrowRecord.BookId,
            BookTitle = book?.Title ?? "Unknown",
            BookAuthor = book?.Author ?? "Unknown",
            MemberId = borrowRecord.MemberId,
            MemberName = member?.FullName ?? "Unknown",
            BorrowDate = borrowRecord.BorrowDate,
            DueDate = borrowRecord.DueDate,
            ReturnDate = borrowRecord.ReturnDate,
            Status = borrowRecord.Status,
            StatusName = GetStatusName(borrowRecord.Status),
            Notes = borrowRecord.Notes,
            Fine = borrowRecord.Fine,
            IsOverdue = borrowRecord.IsOverdue,
            DaysOverdue = borrowRecord.DaysOverdue,
            CreatedAt = borrowRecord.CreatedAt
        };

        return Ok(borrowRecordDto);
    }

    [HttpPost]
    public async Task<ActionResult<BorrowRecordDto>> CreateBorrowRecord(CreateBorrowRecordDto createDto)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(createDto.BookId);
        if (book == null)
        {
            return BadRequest("Không tìm thấy sách");
        }

        if (book.StockQuantity <= 0)
        {
            return BadRequest("Sách đã hết trong kho");
        }

        // Validate member exists and is active
        var member = await _unitOfWork.Members.GetByIdAsync(createDto.MemberId);
        if (member == null)
        {
            return BadRequest("Không tìm thấy thành viên");
        }

        if (member.Status != MemberStatus.Active)
        {
            return BadRequest("Thành viên không hoạt động");
        }

        // Check if member has overdue books
        var memberOverdueBooks = await _unitOfWork.BorrowRecords.FindAsync(br =>
            br.MemberId == createDto.MemberId && br.ReturnDate == null && br.DueDate < DateTime.Now);

        if (memberOverdueBooks.Any())
        {
            return BadRequest("Thành viên có sách quá hạn chưa trả");
        }

        // Create borrow record
        var borrowRecord = new BorrowRecord
        {
            BookId = createDto.BookId,
            MemberId = createDto.MemberId,
            BorrowDate = DateTime.Now,
            DueDate = createDto.DueDate,
            Status = BorrowStatus.Borrowed,
            Notes = createDto.Notes,
            CreatedAt = DateTime.Now
        };

        await _unitOfWork.BorrowRecords.AddAsync(borrowRecord);
        
        // Đơn giản hóa: chỉ cần trừ stock quantity
        book.StockQuantity -= 1;
        await _unitOfWork.Books.UpdateAsync(book);


        await _unitOfWork.SaveChangesAsync();

        // Return the created record with full details
        var result = new BorrowRecordDto
        {
            Id = borrowRecord.Id,
            BookId = borrowRecord.BookId,
            BookTitle = book.Title,
            BookAuthor = book.Author,
            MemberId = borrowRecord.MemberId,
            MemberName = member.FullName,
            BorrowDate = borrowRecord.BorrowDate,
            DueDate = borrowRecord.DueDate,
            ReturnDate = borrowRecord.ReturnDate,
            Status = borrowRecord.Status,
            StatusName = GetStatusName(borrowRecord.Status),
            Notes = borrowRecord.Notes,
            Fine = borrowRecord.Fine,
            IsOverdue = borrowRecord.IsOverdue,
            DaysOverdue = borrowRecord.DaysOverdue,
            CreatedAt = borrowRecord.CreatedAt
        };

        return CreatedAtAction(nameof(GetBorrowRecord), new { id = borrowRecord.Id }, result);
    }

    [HttpPut("{id}/return")]
    public async Task<ActionResult<BorrowRecordDto>> ReturnBook(int id, ReturnBookDto returnDto)
    {
        if (id != returnDto.BorrowRecordId)
        {
            return BadRequest("ID không khớp");
        }

        var borrowRecord = await _unitOfWork.BorrowRecords.GetByIdAsync(id);
        if (borrowRecord == null)
        {
            return NotFound();
        }

        if (borrowRecord.ReturnDate.HasValue)
        {
            return BadRequest("Sách đã được trả");
        }

        // Update borrow record
        borrowRecord.ReturnDate = returnDto.ReturnDate;
        borrowRecord.Status = BorrowStatus.Returned;
        borrowRecord.UpdatedAt = DateTime.Now;

        // Calculate fine if overdue
        if (returnDto.ReturnDate > borrowRecord.DueDate)
        {
            var daysOverdue = (int)Math.Floor((returnDto.ReturnDate - borrowRecord.DueDate).TotalDays);
            borrowRecord.Fine = returnDto.Fine ?? (daysOverdue * 5000); // 5000 VND per day
        }
        else
        {
            // No fine if returned on time or early
            borrowRecord.Fine = 0;
        }

        if (!string.IsNullOrEmpty(returnDto.Notes))
        {
            borrowRecord.Notes = string.IsNullOrEmpty(borrowRecord.Notes)
                ? returnDto.Notes
                : $"{borrowRecord.Notes}; {returnDto.Notes}";
        }

        await _unitOfWork.BorrowRecords.UpdateAsync(borrowRecord);

        // Đơn giản hóa: chỉ cần tăng stock quantity khi trả sách
        var book = await _unitOfWork.Books.GetByIdAsync(borrowRecord.BookId);
        if (book != null)
        {
            book.StockQuantity += 1;
            await _unitOfWork.Books.UpdateAsync(book);
        }

        await _unitOfWork.SaveChangesAsync();

        // Get related data for response
        var member = await _unitOfWork.Members.GetByIdAsync(borrowRecord.MemberId);

        var result = new BorrowRecordDto
        {
            Id = borrowRecord.Id,
            BookId = borrowRecord.BookId,
            BookTitle = book?.Title ?? "Unknown",
            BookAuthor = book?.Author ?? "Unknown",
            MemberId = borrowRecord.MemberId,
            MemberName = member?.FullName ?? "Unknown",
            BorrowDate = borrowRecord.BorrowDate,
            DueDate = borrowRecord.DueDate,
            ReturnDate = borrowRecord.ReturnDate,
            Status = borrowRecord.Status,
            StatusName = GetStatusName(borrowRecord.Status),
            Notes = borrowRecord.Notes,
            Fine = borrowRecord.Fine,
            IsOverdue = borrowRecord.IsOverdue,
            DaysOverdue = borrowRecord.DaysOverdue,
            CreatedAt = borrowRecord.CreatedAt
        };

        return Ok(result);
    }

    [HttpPost("{id}/renew")]
    public async Task<ActionResult<BorrowRecordDto>> RenewBook(int id)
    {
        var borrowRecord = await _unitOfWork.BorrowRecords.GetByIdAsync(id);
        if (borrowRecord == null)
        {
            return NotFound("Không tìm thấy thông tin mượn sách");
        }

        if (borrowRecord.Status != BorrowStatus.Borrowed)
        {
            return BadRequest("Chỉ có thể gia hạn sách đang được mượn");
        }

        if (borrowRecord.Status == BorrowStatus.Overdue)
        {
            return BadRequest("Không thể gia hạn sách đã quá hạn");
        }

        // Kiểm tra xem sách đã được gia hạn trước đó chưa
        if (borrowRecord.Status == BorrowStatus.Renewed)
        {
            return BadRequest("Sách này đã được gia hạn một lần");
        }

        // Gia hạn thêm 14 ngày từ ngày hết hạn cũ
        borrowRecord.DueDate = borrowRecord.DueDate.AddDays(14);
        borrowRecord.Status = BorrowStatus.Renewed;
        borrowRecord.UpdatedAt = DateTime.UtcNow;

        await _unitOfWork.SaveChangesAsync();

        return Ok(new BorrowRecordDto
        {
            Id = borrowRecord.Id,
            BookId = borrowRecord.BookId,
            BookTitle = borrowRecord.Book.Title,
            MemberId = borrowRecord.MemberId,
            MemberName = $"{borrowRecord.Member.FirstName} {borrowRecord.Member.LastName}",
            BorrowDate = borrowRecord.BorrowDate,
            DueDate = borrowRecord.DueDate,
            ReturnDate = borrowRecord.ReturnDate,
            Status = borrowRecord.Status,
            StatusName = GetStatusName(borrowRecord.Status),
            Fine = borrowRecord.Fine,
            Notes = borrowRecord.Notes,
            CreatedAt = borrowRecord.CreatedAt,
            UpdatedAt = borrowRecord.UpdatedAt
        });
    }

    [HttpGet("overdue")]
    public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> GetOverdueBooks()
    {
        var overdueRecords = await _unitOfWork.BorrowRecords.FindAsync(br =>
            br.ReturnDate == null && br.DueDate < DateTime.Now);

        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();

        var bookDict = books.ToDictionary(b => b.Id, b => b);
        var memberDict = members.ToDictionary(m => m.Id, m => m);

        var overdueDtos = overdueRecords.Select(br => new BorrowRecordDto
        {
            Id = br.Id,
            BookId = br.BookId,
            BookTitle = bookDict.GetValueOrDefault(br.BookId)?.Title ?? "Unknown",
            BookAuthor = bookDict.GetValueOrDefault(br.BookId)?.Author ?? "Unknown",
            MemberId = br.MemberId,
            MemberName = memberDict.GetValueOrDefault(br.MemberId)?.FullName ?? "Unknown",
            BorrowDate = br.BorrowDate,
            DueDate = br.DueDate,
            ReturnDate = br.ReturnDate,
            Status = BorrowStatus.Overdue, // Override status for overdue
            StatusName = "Quá hạn",
            Notes = br.Notes,
            Fine = br.Fine,
            IsOverdue = true,
            DaysOverdue = br.DaysOverdue,
            CreatedAt = br.CreatedAt
        }).OrderByDescending(br => br.DaysOverdue).ToList();

        return Ok(overdueDtos);
    }

    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> SearchBorrowRecords(
        [FromQuery] string? memberName,
        [FromQuery] string? bookTitle,
        [FromQuery] BorrowStatus? status,
        [FromQuery] bool? overdue)
    {
        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();

        var bookDict = books.ToDictionary(b => b.Id, b => b);
        var memberDict = members.ToDictionary(m => m.Id, m => m);

        var filteredRecords = borrowRecords.AsEnumerable();

        if (!string.IsNullOrEmpty(memberName))
        {
            filteredRecords = filteredRecords.Where(br =>
                memberDict.GetValueOrDefault(br.MemberId)?.FullName.Contains(memberName, StringComparison.OrdinalIgnoreCase) == true);
        }

        if (!string.IsNullOrEmpty(bookTitle))
        {
            filteredRecords = filteredRecords.Where(br =>
                bookDict.GetValueOrDefault(br.BookId)?.Title.Contains(bookTitle, StringComparison.OrdinalIgnoreCase) == true);
        }

        if (status.HasValue)
        {
            filteredRecords = filteredRecords.Where(br => br.Status == status.Value);
        }

        if (overdue.HasValue)
        {
            if (overdue.Value)
            {
                filteredRecords = filteredRecords.Where(br => br.IsOverdue);
            }
            else
            {
                filteredRecords = filteredRecords.Where(br => !br.IsOverdue);
            }
        }

        var borrowRecordDtos = filteredRecords.Select(br => new BorrowRecordDto
        {
            Id = br.Id,
            BookId = br.BookId,
            BookTitle = bookDict.GetValueOrDefault(br.BookId)?.Title ?? "Unknown",
            BookAuthor = bookDict.GetValueOrDefault(br.BookId)?.Author ?? "Unknown",
            MemberId = br.MemberId,
            MemberName = memberDict.GetValueOrDefault(br.MemberId)?.FullName ?? "Unknown",
            BorrowDate = br.BorrowDate,
            DueDate = br.DueDate,
            ReturnDate = br.ReturnDate,
            Status = br.Status,
            StatusName = GetStatusName(br.Status),
            Notes = br.Notes,
            Fine = br.Fine,
            IsOverdue = br.IsOverdue,
            DaysOverdue = br.DaysOverdue,
            CreatedAt = br.CreatedAt
        }).OrderByDescending(br => br.BorrowDate).ToList();

        return Ok(borrowRecordDtos);
    }

    private static string GetStatusName(BorrowStatus status)
    {
        return status switch
        {
            BorrowStatus.Borrowed => "Đang mượn",
            BorrowStatus.Returned => "Đã trả",
            BorrowStatus.Overdue => "Quá hạn",
            BorrowStatus.Lost => "Mất sách",
            BorrowStatus.Renewed => "Gia hạn",
            _ => "Không xác định"
        };
    }
}