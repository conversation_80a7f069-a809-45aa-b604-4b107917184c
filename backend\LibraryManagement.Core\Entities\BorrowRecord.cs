using LibraryManagement.Core.Enums;

namespace LibraryManagement.Core.Entities;

public class BorrowRecord : BaseEntity
{
    public int BookId { get; set; }
    public int MemberId { get; set; }
    public DateTime BorrowDate { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? ReturnDate { get; set; }
    public BorrowStatus Status { get; set; }
    public string? Notes { get; set; }
    public decimal? Fine { get; set; }
    
    public bool IsOverdue => DueDate < DateTime.Now && ReturnDate == null;
    public int DaysOverdue => IsOverdue ? (int)Math.Floor((DateTime.Now - DueDate).TotalDays) : 0;
    
    // Navigation properties
    public virtual Book Book { get; set; } = null!;
    public virtual Member Member { get; set; } = null!;
} 